<template>
    <alert-content :on-default-save="handleSave">
        <n-form
            ref="formRef"
            :model="formData"
            :rules="rules"
            label-placement="left"
            require-mark-placement="left"
            :label-width="120"
            :show-feedback="false"
        >
            <n-grid :cols="24" :x-gap="10" :y-gap="10">
                <!-- 申请人 -->
                <n-form-item-gi label="申请人" :span="12">
                    <n-input v-model:value="formData.applicant" placeholder="张三" readonly />
                </n-form-item-gi>

                <!-- 申请日期 -->
                <n-form-item-gi label="申请日期" :span="12">
                    <n-date-picker
                        v-model:value="formData.applyDate"
                        type="date"
                        format="yyyy-MM-dd"
                        placeholder="2025-05-02"
                        disabled
                        class="w-full"
                    />
                </n-form-item-gi>

                <!-- 借阅日期 -->
                <n-form-item-gi label="借阅日期" path="borrowPeriod" :span="24">
                    <n-date-picker
                        v-model:value="formData.borrowPeriod"
                        type="daterange"
                        format="yyyy-MM-dd"
                        placeholder="开始日期-结束日期"
                        class="w-full"
                    />
                </n-form-item-gi>

                <!-- 借阅原因 -->
                <n-form-item-gi label="借阅原因" path="borrowReason" :span="24">
                    <n-select
                        v-model:value="formData.borrowReason"
                        :options="borrowReasonOptions"
                        placeholder="请选择"
                        class="w-200px"
                    />
                    <n-input
                        v-if="formData.borrowReason === 'other'"
                        v-model:value="formData.otherReason"
                        placeholder="请输入其他原因"
                        class="ml-10px flex-1"
                    />
                </n-form-item-gi>
            </n-grid>
        </n-form>

        <!-- 文件列表表格 -->
        <div class="mt-20px">
            <div class="flex items-center justify-between mb-10px">
                <span class="text-16px font-600">文件列表</span>
                <n-button type="primary" size="small" @click="handleAddFile">添加文件</n-button>
            </div>

            <n-data-table
                :columns="fileColumns"
                :data="formData.fileList"
                :pagination="false"
                size="small"
                :scroll-x="1200"
                class="file-table"
            />
        </div>
    </alert-content>
</template>

<script setup lang="ts">
import { type DataTableColumns, type FormRules } from 'naive-ui';
import { BorrowingApplicationForm, BorrowingFileItem } from '@/api/apis/nebula/api/v1/borrowing-application';

const props = defineProps<{
    row?: BorrowingApplicationForm;
}>();

const emit = defineEmits<{
    (e: 'submit'): void;
}>();

const formRef = ref();

// 表单数据
const formData = ref<BorrowingApplicationForm>({
    applicant: '张三',
    applyDate: Date.now(),
    borrowPeriod: '',
    borrowReason: '',
    otherReason: '',
    fileList: [
        {
            id: '1',
            fileType: '',
            fileCategory: '',
            fileName: '',
            fileNumber: '',
            fileVersion: '',
            status: ''
        },
        {
            id: '2',
            fileType: '',
            fileCategory: '',
            fileName: '',
            fileNumber: '',
            fileVersion: '',
            status: ''
        }
    ]
});

// 借阅原因选项
const borrowReasonOptions = [
    { label: '工作需要', value: 'work' },
    { label: '学习研究', value: 'study' },
    { label: '项目参考', value: 'project' },
    { label: '其他', value: 'other' }
];

// 表单验证规则
const rules: FormRules = {
    borrowPeriod: [
        { required: true, message: '请输入借阅日期', trigger: 'blur' }
    ],
    borrowReason: [
        { required: true, message: '请选择借阅原因', trigger: 'change' }
    ]
};

// 文件列表表格列定义
const fileColumns: DataTableColumns<BorrowingFileItem> = [
    { title: '序号', key: 'index', align: 'center', width: 60, render: (_, index) => index + 1 },
    {
        title: '文件属性',
        key: 'fileType',
        align: 'center',
        width: 120,
        render: (row, index) => h('n-select', {
            value: row.fileType,
            options: [
                { label: '选择有效性', value: 'validity' },
                { label: '选择文件类型', value: 'type' }
            ],
            placeholder: '选择有效性',
            onUpdateValue: (value: string) => {
                formData.value.fileList![index].fileType = value;
            }
        })
    },
    {
        title: '文件类型',
        key: 'fileCategory',
        align: 'center',
        width: 120,
        render: (row, index) => h('n-select', {
            value: row.fileCategory,
            options: [
                { label: '选择文件类型', value: 'doc' },
                { label: '选择文件类别', value: 'category' }
            ],
            placeholder: '选择文件类型',
            onUpdateValue: (value: string) => {
                formData.value.fileList![index].fileCategory = value;
            }
        })
    },
    {
        title: '文件类别',
        key: 'fileName',
        align: 'center',
        width: 120,
        render: (row, index) => h('n-select', {
            value: row.fileName,
            options: [
                { label: '选择文件类别', value: 'type1' },
                { label: '选择文件名称', value: 'type2' }
            ],
            placeholder: '选择文件类别',
            onUpdateValue: (value: string) => {
                formData.value.fileList![index].fileName = value;
            }
        })
    },
    {
        title: '文件名称',
        key: 'fileNumber',
        align: 'center',
        width: 150,
        render: (row, index) => h('n-select', {
            value: row.fileNumber,
            options: [
                { label: '选择文件名称', value: 'name1' },
                { label: '选择文件编号', value: 'name2' }
            ],
            placeholder: '选择文件名称',
            onUpdateValue: (value: string) => {
                formData.value.fileList![index].fileNumber = value;
            }
        })
    },
    {
        title: '文件编号',
        key: 'fileVersion',
        align: 'center',
        width: 150,
        render: (row, index) => h('n-input', {
            value: row.fileVersion,
            placeholder: '选择文件编号',
            onUpdateValue: (value: string) => {
                formData.value.fileList![index].fileVersion = value;
            }
        })
    },
    {
        title: '版本/状态',
        key: 'status',
        align: 'center',
        width: 120,
        render: (row, index) => h('n-select', {
            value: row.status,
            options: [
                { label: '有效', value: 'valid' },
                { label: '无效', value: 'invalid' }
            ],
            placeholder: '选择状态',
            onUpdateValue: (value: string) => {
                formData.value.fileList![index].status = value;
            }
        })
    },
    {
        title: '操作',
        key: 'action',
        align: 'center',
        width: 80,
        render: (_, index) => h('n-button', {
            type: 'error',
            size: 'tiny',
            onClick: () => handleRemoveFile(index)
        }, { default: () => '删除' })
    }
];

// 添加文件
const handleAddFile = () => {
    formData.value.fileList?.push({
        id: String(Date.now()),
        fileType: '',
        fileCategory: '',
        fileName: '',
        fileNumber: '',
        fileVersion: '',
        status: ''
    });
};

// 删除文件
const handleRemoveFile = (index: number) => {
    formData.value.fileList?.splice(index, 1);
};

// 保存表单
const handleSave = async () => {
    await formRef.value?.validate();

    try {
        // 暂时使用模拟API，实际项目中需要替换为真实API
        if (props.row?.id) {
            // 编辑
            console.log('编辑借阅申请:', formData.value);
            window.$message.success('修改成功');
        } else {
            // 新增
            console.log('新增借阅申请:', formData.value);
            window.$message.success('新增成功');
        }
        emit('submit');
    } catch (error) {
        console.error('保存失败:', error);
    }
};

// 初始化数据
onMounted(() => {
    if (props.row) {
        formData.value = { ...props.row };
    }
});
</script>

<style scoped lang="less">
.file-table {
    :deep(.n-data-table-th) {
        background-color: #f5f7fa;
    }
}
</style>